//
// Created on 2025/6/11.
//
// Node APIs are not fully supported. To solve the compilation error of the interface cannot be found,
// please include "napi/native_api.h".

/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "plugin_manager.h"

// 引入 XComponent 原生接口，用于与 ArkUI 框架的 XComponent 组件进行交互
#include <ace/xcomponent/native_interface_xcomponent.h>
// 引入标准整型定义
#include <cstdint>
// 引入标准 C 语言的输入输出操作
#include <cstdio>
// 引入日志头文件，用于日志输出
#include <hilog/log.h>
// 引入字符串操作
#include <string>
#include <unistd.h>
// 引入 ArkUI 原生节点相关头文件
#include "arkui/native_node.h"
#include "arkui/native_node_napi.h"
#include "arkui/native_interface.h"
#include <sys/stat.h>
#include <errno.h>  // 添加错误码定义

// 定义列边距，用于 UI 布局
#define COLUMN_MARGIN 10
// 定义 XComponent 宽度
#define XC_WIDTH 300
// 定义 XComponent 高度
#define XC_HEIGHT 300
// 定义 NAPI 函数的参数数量
#define ARG_CNT 1
const unsigned int LOG_PRINT_DOMAIN = 0xFF00;

// PluginManager 的单例实例初始化
PluginManager PluginManager::pluginManager_;
// XComponent 回调函数结构体初始化
OH_NativeXComponent_Callback PluginManager::callback_;
// 编辑器实例映射初始化
std::unordered_map<int64_t, Scintilla::Internal::ScintillaHarmony*> PluginManager::editorMap_;
// 下一个编辑器ID初始化
int64_t PluginManager::nextEditorId_ = 1;
// ArkUI Native Node API 的全局指针，用于操作原生节点
static ArkUI_NativeNodeAPI_1* nodeAPI;
// ArkUI 节点句柄，可能用于存储 XComponent 节点实例
ArkUI_NodeHandle xc;
// 静态成员变量，标记是否已进行绘制操作，初始化为 0 (未绘制)
int32_t PluginManager::hasDraw_ = 0;
// 静态成员变量，标记是否已进行颜色改变操作，初始化为 0 (未改变颜色)
int32_t PluginManager::hasChangeColor_ = 0;

/**
 * @brief 将 NAPI 值转换为 std::string。
 * @param env NAPI 环境。
 * @param value 待转换的 NAPI 值。
 * @return 转换后的 std::string。
 */
static std::string value2String(napi_env env, napi_value value)
{
    size_t stringSize = 0;
    // 获取 NAPI 字符串的长度
    napi_get_value_string_utf8(env, value, nullptr, 0, &stringSize);
    std::string valueString;
    // 调整字符串大小以容纳内容
    valueString.resize(stringSize);
    // 将 NAPI 字符串内容复制到 std::string
    napi_get_value_string_utf8(env, value, &valueString[0], stringSize+1, &stringSize);
    return valueString;
}

/**
 * @brief NAPI 接口：获取 XComponent 的绘制和颜色改变状态。
 *        此函数提供给 JS 层调用，以获取 XComponent 的当前渲染状态。
 * @param env NAPI 环境。
 * @param info NAPI 回调信息。
 * @return 返回一个 NAPI 对象，包含 'hasDraw' 和 'hasChangeColor' 两个属性。
 */
napi_value PluginManager::GetXComponentStatus(napi_env env, napi_callback_info info)
{
    napi_value hasDraw;
    napi_value hasChangeColor;

    // 创建 NAPI 整数 hasDraw
    napi_status ret = napi_create_int32(env, hasDraw_, &(hasDraw));
    if (ret != napi_ok) {
        OH_LOG_Print(
            LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "GetXComponentStatus", "napi_create_int32 hasDraw_ error");
        return nullptr;
    }
    // 创建 NAPI 整数 hasChangeColor
    ret = napi_create_int32(env, hasChangeColor_, &(hasChangeColor));
    if (ret != napi_ok) {
        OH_LOG_Print(
            LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "GetXComponentStatus", "napi_create_int32 hasChangeColor_ error");
        return nullptr;
    }

    napi_value obj;
    // 创建 NAPI 对象
    ret = napi_create_object(env, &obj);
    if (ret != napi_ok) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "GetXComponentStatus", "napi_create_object error");
        return nullptr;
    }
    // 设置对象的 hasDraw 属性
    ret = napi_set_named_property(env, obj, "hasDraw", hasDraw);
    if (ret != napi_ok) {
        OH_LOG_Print(
            LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "GetXComponentStatus", "napi_set_named_property hasDraw error");
        return nullptr;
    }
    // 设置对象的 hasChangeColor 属性
    ret = napi_set_named_property(env, obj, "hasChangeColor", hasChangeColor);
    if (ret != napi_ok) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "GetXComponentStatus",
            "napi_set_named_property hasChangeColor error");
        return nullptr;
    }
    return obj;
}

/**
 * @brief NAPI 接口：触发 XComponent 绘制图案。
 *        此函数提供给 JS 层调用，以请求 XComponent 执行一次绘制操作，通常是更新渲染内容。
 * @param env NAPI 环境。
 * @param info NAPI 回调信息。
 * @return 返回 nullptr。
 */
napi_value PluginManager::NapiDrawPattern(napi_env env, napi_callback_info info)
{
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager", "NapiDrawPattern");
    if ((env == nullptr) || (info == nullptr)) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "NapiDrawPattern: env or info is null");
        return nullptr;
    }
    napi_value thisArg;
    // 获取 NAPI 回调的 this 参数
    if (napi_get_cb_info(env, info, nullptr, nullptr, &thisArg, nullptr) != napi_ok) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "NapiDrawPattern: napi_get_cb_info fail");
        return nullptr;
    }

    // 获取 PluginManager 单例
    auto *pluginManger = PluginManager::GetInstance();
    // 调用 EGLCore 的 Draw 方法进行绘制，hasDraw_ 作为参数
//    pluginManger->suerfacecore_->Draw(hasDraw_);
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager", "render->eglCore_->Draw() executed");
    
    return nullptr;
}

/**
 * @brief XComponent Surface 创建回调函数。
 *        当 XComponent 的 Surface 被创建时，由 ArkUI 框架调用。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void OnSurfaceCreatedCB(OH_NativeXComponent* component, void* window)
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native", "OnSurfaceCreatedCB");
    int32_t ret;
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;
    // 获取 XComponent 的 ID
    ret = OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    if (ret != OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        return;
    }
    
    std::string id(idStr);
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native", "OnSurfaceCreatedCB id=%{public}s",
                 id.c_str());
    // 获取 PluginManager 单例
    auto *pluginManger = PluginManager::GetInstance();
    // 调用 PluginManager 的 OnSurfaceCreated 方法进行进一步处理
    pluginManger->OnSurfaceCreated(component, window);
}

/**
 * @brief XComponent Surface 尺寸改变回调函数。
 *        当 XComponent 的 Surface 尺寸改变时，由 ArkUI 框架调用。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void OnSurfaceChangedCB(OH_NativeXComponent* component, void* window)
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native", "OnSurfaceChangedCB");
    int32_t ret;
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;
    // 获取 XComponent 的 ID
    ret = OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    if (ret != OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        return;
    }
    std::string id(idStr);
    // 获取 PluginManager 单例
    auto *pluginManger = PluginManager::GetInstance();
    // 调用 PluginManager 的 OnSurfaceChanged 方法进行进一步处理
    pluginManger->OnSurfaceChanged(component, window);
}

/**
 * @brief XComponent Surface 销毁回调函数。
 *        当 XComponent 的 Surface 被销毁时，由 ArkUI 框架调用。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void OnSurfaceDestroyedCB(OH_NativeXComponent* component, void* window)
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "Callback", "OnSurfaceDestroyedCB");
    int32_t ret;
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;
    // 获取 XComponent 的 ID
    ret = OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    if (ret != OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        return;
    }
    std::string id(idStr);
    // 获取 PluginManager 单例
    auto *pluginManger = PluginManager::GetInstance();
    // 调用 PluginManager 的 OnSurfaceDestroyed 方法进行进一步处理
    pluginManger->OnSurfaceDestroyed(component, window);
}

/**
 * @brief XComponent 触摸事件分发回调函数。
 *        当 XComponent 接收到触摸事件时，由 ArkUI 框架调用。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void DispatchTouchEventCB(OH_NativeXComponent* component, void* window)
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "Callback", "DispatchTouchEventCB");
    int32_t ret;
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;
    // 获取 XComponent 的 ID
    ret = OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    if (ret != OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        return;
    }
    std::string id(idStr);
    // 获取 PluginManager 单例
    auto *pluginManger = PluginManager::GetInstance();
    // 调用 PluginManager 的 DispatchTouchEvent 方法进行进一步处理
    pluginManger->DispatchTouchEvent(component, window);
}

/**
 * @brief PluginManager 类的构造函数。
 *        初始化 EGLCore 实例并注册 XComponent 的生命周期回调函数。
 */
PluginManager::PluginManager()
{
    // 创建 EGLCore 实例
    suerfacecore_ = new Surface_Core();
    // 注册 XComponent Surface 创建回调
    callback_.OnSurfaceCreated = OnSurfaceCreatedCB;
    // 注册 XComponent Surface 尺寸改变回调
    callback_.OnSurfaceChanged = OnSurfaceChangedCB;
    // 注册 XComponent Surface 销毁回调
    callback_.OnSurfaceDestroyed = OnSurfaceDestroyedCB;
    // 注册 XComponent 触摸事件分发回调
    callback_.DispatchTouchEvent = DispatchTouchEventCB;
}

/**
 * @brief PluginManager 类的析构函数。
 *        清理资源，包括 EGLCore 实例和映射表。
 */
PluginManager::~PluginManager()
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "Callback", "~PluginManager");
    // 清空 nativeXComponentMap_，释放存储的 OH_NativeXComponent 指针 (这里只清空 map，实际内存释放取决于外部管理)
    nativeXComponentMap_.clear();
    // 释放 EGLCore 实例
    if (suerfacecore_ != nullptr) {
        delete suerfacecore_;
        suerfacecore_ = nullptr;
        }

    // 遍历 pluginManagerMap_，释放存储的 PluginManager 实例 (如果存在)
    for (auto iter = pluginManagerMap_.begin(); iter != pluginManagerMap_.end(); ++iter) {
        if (iter->second != nullptr) {
            delete iter->second;
            iter->second = nullptr;
        }
    }
    // 清空 pluginManagerMap_
    pluginManagerMap_.clear();
}

/**
 * @brief 创建一个 ArkUI Native Node 句柄，通常用于构建 UI 树。
 *        此函数根据传入的 tag 创建一个包含 XComponent 的 Column 节点，并设置相关属性。
 * @param tag 用于标识节点或设置 XComponent 的用户数据。
 * @return 返回创建的 ArkUI_NodeHandle。
 */
ArkUI_NodeHandle CreateNodeHandle(const std::string &tag)
{
    // 创建一个 Column 节点
    ArkUI_NodeHandle column = nodeAPI->createNode(ARKUI_NODE_COLUMN);
    // 定义用于设置节点属性的数值
    ArkUI_NumberValue value[] = {480};
    ArkUI_NumberValue value1[] = {{.u32 = 15}, {.f32 = 15}};
    // 定义属性项，用于设置节点属性
    ArkUI_AttributeItem item = {value, 1, "changeSize"};
    ArkUI_AttributeItem item1 = {value1, 2};
    // 设置 Column 的宽度
    nodeAPI->setAttribute(column, NODE_WIDTH, &item);
    // 设置 Column 的边距
    value[0].f32 = COLUMN_MARGIN;
    nodeAPI->setAttribute(column, NODE_MARGIN, &item);
    // 创建一个 XComponent 节点
    xc = nodeAPI->createNode(ARKUI_NODE_XCOMPONENT);
    // 设置 XComponent 的类型为 Surface 类型
    value[0].u32 = ARKUI_XCOMPONENT_TYPE_SURFACE;
    nodeAPI->setAttribute(xc, NODE_XCOMPONENT_TYPE, &item);
    // 设置 XComponent 的 ID (注意：这里使用同一个 item 可能会覆盖前面的值，需要检查实际需求)
    nodeAPI->setAttribute(xc, NODE_XCOMPONENT_ID, &item);
    // 设置 XComponent 的 Surface 尺寸
    nodeAPI->setAttribute(xc, NODE_XCOMPONENT_SURFACE_SIZE, &item1);
    // 设置 XComponent 为可聚焦
    ArkUI_NumberValue focusable[] = {1};
    focusable[0].i32 = 1;
    ArkUI_AttributeItem focusableItem = {focusable, 1};
    nodeAPI->setAttribute(xc, NODE_FOCUSABLE, &focusableItem);
    // 设置 XComponent 的宽度
    ArkUI_NumberValue valueSize[] = {480};
    ArkUI_AttributeItem itemSize = {valueSize, 1};
    valueSize[0].f32 = XC_WIDTH;
    nodeAPI->setAttribute(xc, NODE_WIDTH, &itemSize);
    // 设置 XComponent 的高度
    valueSize[0].f32 = XC_HEIGHT;
    nodeAPI->setAttribute(xc, NODE_HEIGHT, &itemSize);
    // 设置 XComponent 的节点 ID
    ArkUI_AttributeItem item2 = {value, 1, "ndkxcomponent"};
    nodeAPI->setAttribute(xc, NODE_ID, &item2);
    
    // 获取 OH_NativeXComponent 实例
    auto *nativeXComponent = OH_NativeXComponent_GetNativeXComponent(xc);
    if (!nativeXComponent) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "GetNativeXComponent error");
        return column;
    }
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "GetNativeXComponent success");
    // 注册 XComponent 的回调函数
    OH_NativeXComponent_RegisterCallback(nativeXComponent, &PluginManager::callback_);
    // 获取并打印 XComponent 的类型
    auto typeRet = nodeAPI->getAttribute(xc, NODE_XCOMPONENT_TYPE);
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "xcomponent type: %{public}d",
                 typeRet->value[0].i32);
    // 获取并打印 XComponent 的 ID
    auto idRet = nodeAPI->getAttribute(xc, NODE_XCOMPONENT_ID);
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "xcomponent id: %{public}s",
                 idRet->string);
    // 将 XComponent 节点添加到 Column 节点中
    nodeAPI->addChild(column, xc);
    return column;
}

/**
 * @brief NAPI 接口：创建原生节点。
 *        JS 层通过此函数请求创建 Native UI 节点，并将其与 XComponent 的用户数据关联。
 * @param env NAPI 环境。
 * @param info NAPI 回调信息，其中包含节点内容句柄和用户数据（tag）。
 * @return 返回 nullptr。
 */

napi_value PluginManager::createNativeNode(napi_env env, napi_callback_info info)
{
    if ((env == nullptr) || (info == nullptr)) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "CreateNativeNode env or info is null");
        return nullptr;
    }
    size_t argCnt = 1;
    napi_value args[1] = { nullptr };
    // 获取 NAPI 回调信息和参数
    if (napi_get_cb_info(env, info, &argCnt, args, nullptr, nullptr) != napi_ok) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "CreateNativeNode napi_get_cb_info failed");
    }
    // 检查参数数量是否正确
    if (argCnt != ARG_CNT) {
        napi_throw_type_error(env, NULL, "Wrong number of arguments");
        return nullptr;
    }
    ArkUI_NodeContentHandle nodeContentHandle_ = nullptr;
    // 从 NAPI 值中获取 ArkUI 节点内容句柄
    OH_ArkUI_GetNodeContentFromNapiValue(env, args[0], &nodeContentHandle_);
    // 查询并获取 ArkUI Native Node API 接口
    nodeAPI = reinterpret_cast<ArkUI_NativeNodeAPI_1*>(
        OH_ArkUI_QueryModuleInterfaceByName(ARKUI_NATIVE_NODE, "ArkUI_NativeNodeAPI_1")
    );

    // 如果 nodeAPI 有效，则注册节点内容事件回调
    if (nodeAPI != nullptr && nodeAPI->createNode != nullptr && nodeAPI->addChild != nullptr) {
        // 定义节点内容事件回调函数 (lambda 表达式)
        auto nodeContentEvent = [](ArkUI_NodeContentEvent *event) {
            // 获取节点内容句柄
            ArkUI_NodeContentHandle handle = OH_ArkUI_NodeContentEvent_GetNodeContentHandle(event);
            // 获取用户数据 (tag)
            std::string *userDate = reinterpret_cast<std::string*>(OH_ArkUI_NodeContent_GetUserData(handle));
            // 如果事件类型是依附到窗口
            if (OH_ArkUI_NodeContentEvent_GetEventType(event) == NODE_CONTENT_EVENT_ON_ATTACH_TO_WINDOW) {
                ArkUI_NodeHandle testNode;
                // 如果用户数据存在，则使用用户数据创建节点
                if (userDate) {
                    testNode = CreateNodeHandle(*userDate);
                    delete userDate; // 释放用户数据内存
                    userDate = nullptr;
                } else {
                    // 如果用户数据不存在，则使用默认值创建节点
                    testNode = CreateNodeHandle("noUserData");
                }
                // 将创建的节点添加到节点内容中
                OH_ArkUI_NodeContent_AddNode(handle, testNode);
            }
        };
        // 注册节点内容回调函数
        OH_ArkUI_NodeContent_RegisterCallback(nodeContentHandle_, nodeContentEvent);
    }
    return nullptr;
}

 /**
     * @brief Surface 文本读取。
     * @param component env NAPI 环境。
     * @param info NAPI 回调信息。
*/
napi_value PluginManager::SetText(napi_env env, napi_callback_info info){
    size_t argc = 1;
    napi_value args[1] = { nullptr };
    // 获取 NAPI 回调信息和参数
    if (napi_get_cb_info(env, info, &argc, args, nullptr, nullptr) != napi_ok) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "SetText napi_get_cb_info failed");
    }
    // 将 NAPI 参数中的 tag 转换为 fd
    uint32_t fd = 0;
     if (napi_get_value_uint32(env, args[0], &fd) != napi_ok) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "Failed to get fd value");
        return nullptr;
    }
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager", "tag=%{public}d", fd);
    //获取文本大小
    struct  stat st;
    if(fstat(fd,&st) != 0){
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "Failed to get file size");
        return nullptr;
    }
    // 分配内存并读取文件
    size_t contentsSize = st.st_size;
    std::string contentsBuf;
    contentsBuf.resize(contentsSize);
    // 读取文件内容
    size_t totalRead = 0;
    while (totalRead < contentsSize) {
        ssize_t bytesRead = read(fd, &contentsBuf + totalRead, contentsSize - totalRead);
        if (bytesRead < 0) {
            if (errno == EINTR) {
                continue;  // 被信号中断，重试
            }
            // 其他错误（如权限不足）
            OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager", "Read error: %{public}s", strerror(errno));
            break;
        }
        if (bytesRead == 0) {
            // 到达文件末尾（EOF）
            break;
        }
        totalRead += bytesRead;
    }
    // 验证是否完整读取
    if (totalRead != contentsSize) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager","Incomplete read: %{public}zu/%{public}zu bytes",totalRead, contentsSize);
    }
    close(fd);

    // 将文件内容设置到第一个可用的编辑器实例中
    if (!editorMap_.empty()) {
        auto editor = editorMap_.begin()->second;
        if (editor) {
            // 使用Scintilla的SetText消息设置文本内容
            editor->WndProc(Scintilla::Message::SetText, 0, reinterpret_cast<Scintilla::sptr_t>(contentsBuf.c_str()));

            // 触发重绘
            editor->Redraw();

            OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager",
                        "File content set to editor, size: %{public}zu bytes", totalRead);
        }
    } else {
        OH_LOG_Print(LOG_APP, LOG_WARNING, LOG_PRINT_DOMAIN, "PluginManager",
                    "No editor instance available to set text");
    }

    return nullptr;
}

/**
 * @brief 创建Scintilla编辑器实例。
 * @param env NAPI 环境。
 * @param info NAPI 回调信息。
 * @return 返回编辑器ID。
 */
napi_value PluginManager::CreateScintillaEditor(napi_env env, napi_callback_info info) {
    try {
        // 创建ScintillaHarmony实例
        auto* editor = new Scintilla::Internal::ScintillaHarmony();

        // 生成唯一ID
        int64_t editorId = nextEditorId_++;
        editorMap_[editorId] = editor;

        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager",
                     "Created Scintilla editor with ID: %{public}lld", editorId);

        // 返回编辑器ID
        napi_value result;
        napi_create_int64(env, editorId, &result);
        return result;

    } catch (const std::exception& e) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager",
                     "Failed to create Scintilla editor: %{public}s", e.what());
        napi_value result;
        napi_create_int64(env, -1, &result);
        return result;
    }
}

/**
 * @brief 初始化Scintilla编辑器与XComponent。
 * @param env NAPI 环境。
 * @param info NAPI 回调信息。
 * @return 返回 nullptr。
 */
napi_value PluginManager::InitScintillaEditor(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager",
                    "InitScintillaEditor: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = editorMap_.find(editorId);
    if (it == editorMap_.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager",
                    "InitScintillaEditor: editor not found");
        return nullptr;
    }

    auto* editor = it->second;

    // 获取XComponent指针
    OH_NativeXComponent* component;
    napi_get_value_external(env, args[1], (void**)&component);

    if (component) {
        // 初始化编辑器与XComponent
        editor->Init(component, nullptr);
        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager",
                     "Scintilla editor initialized with XComponent");
    }

    return nullptr;
}

/**
 * @brief 销毁Scintilla编辑器实例。
 * @param env NAPI 环境。
 * @param info NAPI 回调信息。
 * @return 返回 nullptr。
 */
napi_value PluginManager::DestroyScintillaEditor(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "PluginManager",
                    "DestroyScintillaEditor: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = editorMap_.find(editorId);
    if (it != editorMap_.end()) {
        delete it->second;
        editorMap_.erase(it);
        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager",
                     "Destroyed Scintilla editor with ID: %{public}lld", editorId);
    } else {
        OH_LOG_Print(LOG_APP, LOG_WARNING, LOG_PRINT_DOMAIN, "PluginManager",
                     "Editor with ID %{public}lld not found", editorId);
    }

    return nullptr;
}

/**
 * @brief PluginManager 的 OnSurfaceCreated 实现。
 *        当 XComponent 的 Surface 被创建时调用，用于初始化 EGL 上下文和背景绘制。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void PluginManager::OnSurfaceCreated(OH_NativeXComponent* component, void* window)
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native", "PluginManager::OnSurfaceCreated");
    int32_t ret;
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;
    // 获取 XComponent 的 ID (虽然获取了，但这里未用到)
    ret = OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    // 获取 XComponent 的尺寸
    ret = OH_NativeXComponent_GetXComponentSize(component, window, &width_, &height_);
    // 如果成功获取尺寸，则初始化 EGLContext 并绘制背景
    if (ret == OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        suerfacecore_->EglContextInit(window, width_, height_);
        suerfacecore_->Background();

        // 自动初始化第一个可用的Scintilla编辑器
        if (!editorMap_.empty()) {
            auto editor = editorMap_.begin()->second;
            if (editor) {
                editor->Init(component, window);
                OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, "PluginManager",
                            "Auto-initialized Scintilla editor with XComponent");
            }
        }
    }
}

/**
 * @brief PluginManager 的 OnSurfaceDestroyed 实现。
 *        当 XComponent 的 Surface 被销毁时调用。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void PluginManager::OnSurfaceDestroyed(OH_NativeXComponent* component, void* window)
{
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native", "PluginManager::OnSurfaceDestroyed");
}

/**
 * @brief PluginManager 的 DispatchTouchEvent 实现。
 *        当 XComponent 接收到触摸事件时调用，用于处理触摸事件并可能改变颜色。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void PluginManager::DispatchTouchEvent(OH_NativeXComponent* component, void* window)
{
    // 获取触摸事件信息
    int32_t ret = OH_NativeXComponent_GetTouchEvent(component, window, &touchEvent_);
    if (ret == OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        float tiltX = 2.2;
        float tiltY = 2.2;
        OH_NativeXComponent_TouchPointToolType toolType =
            OH_NativeXComponent_TouchPointToolType::OH_NATIVEXCOMPONENT_TOOL_TYPE_LENS;
        // 获取触摸点工具类型
        OH_NativeXComponent_GetTouchPointToolType(component, 0, &toolType);
        // 获取触摸点倾斜 X 值
        OH_NativeXComponent_GetTouchPointTiltX(component, 0, &tiltX);
        // 获取触摸点倾斜 Y 值
        OH_NativeXComponent_GetTouchPointTiltY(component, 0, &tiltY);
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native",
                     "Touch Info : x=%{public}f, y=%{public}f screenx=%{public}f, screeny=%{public}f,"
                     "type=%{public}d, force=%{public}f, tiltX=%{public}f, tiltY=%{public}f, toolType=%{public}d",
                     touchEvent_.x, touchEvent_.y, touchEvent_.screenX,
                     touchEvent_.screenY, touchEvent_.type, touchEvent_.force, tiltX, tiltY, toolType);
        // 如果是触摸抬起事件，则改变颜色
//        if (touchEvent_.type == OH_NativeXComponent_TouchEventType::OH_NATIVEXCOMPONENT_UP)
//            suerfacecore_->ChangeColor(hasChangeColor_);
    } else {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native", "touch fail");
    }
}

/**
 * @brief PluginManager 的 OnSurfaceChanged 实现。
 *        当 XComponent 的 Surface 尺寸改变时调用，更新内部存储的尺寸信息。
 * @param component OH_NativeXComponent 实例指针。
 * @param window 窗口句柄。
 */
void PluginManager::OnSurfaceChanged(OH_NativeXComponent* component, void* window)
{
    // 获取 XComponent 的新尺寸
    int32_t ret = OH_NativeXComponent_GetXComponentSize(component, window, &width_, &height_);
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, "XComponent_Native",
                 "OnSurfaceChanged ret=%{public}d width=%{public}lu, height=%{public}lu", ret, width_, height_);
}
