#ifndef SCINTILLA_HARMONY_H
#define SCINTILLA_HARMONY_H

#include <cstddef>
#include <cstdlib>
#include <cstdint>
#include <cassert>
#include <cstring>
#include <cctype>
#include <cstdio>
#include <ctime>
#include <cmath>
#include <stdexcept>
#include <string>
#include <string_view>
#include <vector>
#include <map>
#include <set>
#include <optional>
#include <algorithm>
#include <memory>

#include "ScintillaTypes.h"
#include "ScintillaMessages.h"
#include "ScintillaStructures.h"
#include "Scintilla.h"
#include "../src/scintilla/Debugging.h"
#include "../src/scintilla/Geometry.h"
#include "../src/scintilla/Platform.h"
#include "./scintilla/ILoader.h"
#include "./scintilla/ILexer.h"
#include "./scintilla/ScintillaMessages.h"
#include "./scintilla/ScintillaTypes.h"
#include "./scintilla/ScintillaStructures.h"
#include "../src/scintilla/CharacterCategoryMap.h"
#include "../src/scintilla/Position.h"
#include "../src/scintilla/UniqueString.h"
#include "../src/scintilla/SplitVector.h"
#include "../src/scintilla/Partitioning.h"
#include "../src/scintilla/RunStyles.h"
#include "../src/scintilla/ContractionState.h"
#include "../src/scintilla/CellBuffer.h"
#include "../src/scintilla/CallTip.h"
#include "../src/scintilla/KeyMap.h"
#include "../src/scintilla/Indicator.h"
#include "../src/scintilla/LineMarker.h"
#include "../src/scintilla/Style.h"
#include "../src/scintilla/AutoComplete.h"
#include "../src/scintilla/ViewStyle.h"
#include "../src/scintilla/CharClassify.h"
#include "../src/scintilla/Decoration.h"
#include "../src/scintilla/CaseFolder.h"
#include "../src/scintilla/Document.h"
#include "../src/scintilla/Selection.h"
#include "../src/scintilla/PositionCache.h"
#include "../src/scintilla/EditModel.h"
#include "../src/scintilla/MarginView.h"
#include "../src/scintilla/EditView.h"
#include "../src/scintilla/Editor.h"
#include "../src/scintilla/ScintillaBase.h"
#include "../src/scintilla/CaseConvert.h"
#include <native_window/external_window.h>
#include <ace/xcomponent/native_interface_xcomponent.h>
#include "platform_harmony.h"

namespace Scintilla::Internal {
class ScintillaHarmony :   public ScintillaBase {
    
public:
    explicit  ScintillaHarmony();
    virtual  ~ScintillaHarmony();
    
public:
    void Init(OH_NativeXComponent* component, void* window);
    void Finalise() override;
//    bool DragThreshold(Point ptStart, Point ptNow) override;
//	bool ValidCodePage(int codePage) const override;
//	std::string UTF8FromEncoded(std::string_view encoded) const override;
//	std::string EncodedFromUTF8(std::string_view utf8) const override;
private:
//    void ScrollText(Sci::Line linesToMove) override;
//	void SetVerticalScrollPos() override;
//	void SetHorizontalScrollPos() override;
//	bool ModifyScrollBars(Sci::Line nMax, Sci::Line nPage) override;
//	void ReconfigureScrollBars() override;
//	void Copy() override;
//	void CopyToClipboard(const SelectionText &selectedText) override;
//    void Paste() override;
//	void ClaimSelection() override;
	void NotifyChange() override;
	void NotifyFocus(bool focus) override;
	void NotifyParent(Scintilla::NotificationData scn) override;
//    bool FineTickerRunning(TickReason reason) override;
//	void FineTickerStart(TickReason reason, int millis, int tolerance) override;
//    void FineTickerCancel(TickReason reason) override;
//    bool SetIdle(bool on) override;
//	void SetMouseCapture(bool on) override;
//	bool HaveMouseCapture() override;
//	void StartDrag() override;
//    std::unique_ptr<CaseFolder> CaseFolderForEncoding() override;
//	std::string CaseMapString(const std::string &s, CaseMapping caseMapping) override;
//
//	void CreateCallTipWindow(PRectangle rc) override;
//	void AddToPopUp(const char *label, int cmd, bool enabled) override;
	sptr_t WndProc(Scintilla::Message iMessage, uptr_t wParam, sptr_t lParam) override;
//	sptr_t DefWndProc(Scintilla::Message iMessage, uptr_t wParam, sptr_t lParam) override;
//    
    
protected:
    void Paint();
    void ChangeSize();

private:
    // 窗口尺寸
    uint64_t width_;
    uint64_t height_;
    OHNativeWindow *nativeWindow;
    int rectangularSelectionModifier = 0;

    // 静态回调函数
    static sptr_t DirectFunction(sptr_t ptr, unsigned int iMessage, uptr_t wParam, sptr_t lParam);
    static sptr_t DirectStatusFunction(sptr_t ptr, unsigned int iMessage, uptr_t wParam, sptr_t lParam, int* pStatus);

public:
    PlatformHarmony* surface;

    // 便利方法
    void SetText(const char* text);
    void GetText(int length, char* text);
    Sci::Position Length();
    Sci::Position CurrentPosition();
    void InsertString(Sci::Position pos, const char* s, Sci::Position insertLength);
    void StartStyling(Sci::Position pos);
    void SetStyling(Sci::Position length, int style);
    void Redraw();
};
}

#endif