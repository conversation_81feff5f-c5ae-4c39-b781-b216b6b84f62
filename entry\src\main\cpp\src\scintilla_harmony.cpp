#include "../include/scintilla_harmony.h"
#include "../include/platform_harmony.h"
#include <native_drawing/drawing_text_typography.h>
#include <hilog/log.h>

using namespace Scintilla;
using namespace Scintilla::Internal;

#define LOG_PRINT_DOMAIN 0xFF00
#define LOG_TAG "ScintillaHarmony"

ScintillaHarmony::ScintillaHarmony() : nativeWindow(nullptr) {
    surface = new PlatformHarmony();
    // 初始化编辑器
    Initialise();
}

ScintillaHarmony::~ScintillaHarmony(){
    Finalise();
    if (surface) {
        delete surface;
        surface = nullptr;
    }
}

void ScintillaHarmony::Init(OH_NativeXComponent* component, void* window){
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;

    OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    int32_t ret = OH_NativeXComponent_GetXComponentSize(component, window, &width_, &height_);

    if (ret == OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        nativeWindow = static_cast<OHNativeWindow*>(window);

        // 初始化绘制表面
        if (surface) {
            static_cast<SurfaceHarmony*>(surface)->Init(window, width_, height_);
        }

        // 设置编辑器窗口大小
        ChangeSize();

        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG,
                     "ScintillaHarmony initialized with size: %{public}llu x %{public}llu",
                     width_, height_);
    } else {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Failed to get XComponent size");
    }
}

void ScintillaHarmony::Finalise(){
    // 销毁资源
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG, "Finalising ScintillaHarmony");

    if (surface) {
        surface->Release();
    }

    ScintillaBase::Finalise();
}

void ScintillaHarmony::NotifyChange(){
    // 通知内容发生变化
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Content changed");
}

void ScintillaHarmony::NotifyParent(NotificationData scn){
    // 将通用平台的信息转换为鸿蒙平台需要的信息
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Notify parent: code=%{public}d", static_cast<int>(scn.nmhdr.code));

    // 这里可以添加向JavaScript层发送通知的逻辑
}

void ScintillaHarmony::NotifyFocus(bool focus){
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Focus changed: %{public}s", focus ? "gained" : "lost");
}


sptr_t ScintillaHarmony::WndProc(Message iMessage, uptr_t wParam, sptr_t lParam)
{
	try {
		switch (iMessage) {

		case Message::SetIMEInteraction:
			// 鸿蒙平台IME交互设置
			OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetIMEInteraction");
			break;

		case Message::GrabFocus:
			// 获取焦点
			OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "GrabFocus");
			SetFocusState(true);
			break;

		case Message::GetDirectFunction:
			// 返回直接函数指针
			return reinterpret_cast<sptr_t>(DirectFunction);

		case Message::GetDirectStatusFunction:
			// 返回直接状态函数指针
			return reinterpret_cast<sptr_t>(DirectStatusFunction);

		case Message::GetDirectPointer:
			// 返回当前实例指针
			return reinterpret_cast<sptr_t>(this);

		case Message::SetRectangularSelectionModifier:
			// 设置矩形选择修饰符
			rectangularSelectionModifier = static_cast<int>(wParam);
			break;

		case Message::GetRectangularSelectionModifier:
			// 获取矩形选择修饰符
			return rectangularSelectionModifier;

		// 移除不存在的消息类型
		// case Message::Paint:
		// case Message::SetSize:
		// 这些消息在Scintilla中不存在，由其他机制处理

		default:
			return ScintillaBase::WndProc(iMessage, wParam, lParam);
		}
	} catch (std::bad_alloc &) {
		errorStatus = Status::BadAlloc;
	} catch (...) {
		errorStatus = Status::Failure;
	}
	return 0;
}

// 添加一些辅助方法
void ScintillaHarmony::Paint() {
    if (!surface || !surface->Initialised()) {
        return;
    }

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Paint called");

    // 简化的绘制实现
    // 实际的绘制会通过Scintilla的内部机制处理
    // 这里只是一个占位符方法
}

void ScintillaHarmony::ChangeSize() {
    if (width_ > 0 && height_ > 0) {
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "ChangeSize: %{public}llu x %{public}llu", width_, height_);

        // 设置编辑器大小
        PRectangle rc(0, 0, width_, height_);
        SetScrollBars();
        // 使用正确的方法名
        ChangeScrollBars();
    }
}

// 静态回调函数
sptr_t ScintillaHarmony::DirectFunction(sptr_t ptr, unsigned int iMessage, uptr_t wParam, sptr_t lParam) {
    ScintillaHarmony* sci = reinterpret_cast<ScintillaHarmony*>(ptr);
    return sci->WndProc(static_cast<Message>(iMessage), wParam, lParam);
}

sptr_t ScintillaHarmony::DirectStatusFunction(sptr_t ptr, unsigned int iMessage, uptr_t wParam, sptr_t lParam, int* pStatus) {
    ScintillaHarmony* sci = reinterpret_cast<ScintillaHarmony*>(ptr);
    sptr_t result = sci->WndProc(static_cast<Message>(iMessage), wParam, lParam);
    *pStatus = static_cast<int>(sci->errorStatus);
    return result;
}